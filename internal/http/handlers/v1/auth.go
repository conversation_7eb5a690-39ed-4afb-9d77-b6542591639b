package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/jwtauth/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/helpers"

	"github.com/izy-mercado/backend/internal/http/common"
	custom_errors "github.com/izy-mercado/backend/internal/http/common/errors"
	mailer "github.com/izy-mercado/backend/internal/integrations/mailer"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/jackc/pgx/v4/pgxpool"
)

type AuthHandler struct {
	queries *postgres.Queries
	pool    *pgxpool.Pool
	env     *config.Environment
}

type LoginRequest struct {
	Email     string `json:"email" example:"<EMAIL>" validate:"required"`
	LoginCode string `json:"login_code" example:"12345" validate:"required"`
}

type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

type SendLoginCodeRequest struct {
	Email string `json:"email" example:"<EMAIL>" validate:"required"`
}

type LoginResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type RefreshResponse struct {
	AccessToken string `json:"access_token"`
}

type LoginSuccessResponse = common.SuccessResponse[LoginResponse]
type RefreshSuccessResponse = common.SuccessResponse[RefreshResponse]
type EmptySuccessResponse = common.SuccessResponse[*interface{}]

type JWTClaims struct {
	ExternalID string `json:"external_id"`
}

func NewAuthHandler(env *config.Environment, queries *postgres.Queries, pool *pgxpool.Pool) *chi.Mux {
	router := chi.NewRouter()
	h := &AuthHandler{
		queries: queries,
		pool:    pool,
		env:     env,
	}

	m := middlewares.New(env, queries)

	router.Group(func(r chi.Router) {
		r.Post("/send-login-code", h.SendLoginCode)

		// Routes that require application type header
		appTypeRouter := r.With(m.ApplicationTypeMiddleware)
		appTypeRouter.Post("/login", h.Login)
		appTypeRouter.Post("/refresh", h.Refresh)

		defaultRouter := r.With(m.DefaultPermissions)
		defaultRouter.Post("/logout", h.Logout)
	})

	return router
}

// getApplicationTypeFromContext extracts application type from request context
func getApplicationTypeFromContext(r *http.Request) string {
	if appType := r.Context().Value(middlewares.ApplicationTypeContextKey); appType != nil {
		return appType.(string)
	}
	return ""
}

// validateApplicationAccess checks if user has permission to access the specified application type
func (h *AuthHandler) validateApplicationAccess(ctx context.Context, userExternalID, applicationType string) error {
	// Get user roles
	roles, err := h.queries.GetUserRoles(ctx, userExternalID)
	if err != nil {
		return fmt.Errorf("failed to get user roles: %w", err)
	}

	// Check application access based on roles
	switch applicationType {
	case "mobile":
		// Mobile app requires user or admin role
		for _, role := range roles {
			if role == "user" || role == "admin" {
				return nil
			}
		}
		return errors.New("insufficient permissions: user role required for mobile application")
	case "partner-web":
		// Partner web requires partner or admin role
		for _, role := range roles {
			if role == "partner" || role == "admin" {
				return nil
			}
		}
		return errors.New("insufficient permissions: partner role required for partner-web application")
	default:
		return errors.New("invalid application type")
	}
}

// Login godoc
// @Summary Login
// @Description Login
// @Tags Auth
// @Accept json
// @Produce json
// @Param X-Application-Type header string true "Application type (e.g., mobile, partner-web)"
// @Param body body LoginRequest true "Login payload"
// @Success 200 {object} LoginSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/auth/login [post]
func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	payload := LoginRequest{}

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helpers.New().ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	user, err := h.queries.GetUserByEmail(r.Context(), payload.Email)
	if err != nil {
		log.Println("Failed to get user by email: ", err)
		common.RespondError(w, err)
		return
	}

	if user.ID == 0 {
		common.RespondError(w, errors.New(custom_errors.ErrUserNotFound), http.StatusBadRequest)
		return
	}

	if user.IsDeleted {
		common.RespondError(w, errors.New(custom_errors.ErrUserNotActive), http.StatusBadRequest)
		return
	}

	// TODO: add demo user for apple review - bypass login code check only
	if payload.Email != "<EMAIL>" && user.LoginCode != payload.LoginCode {
		common.RespondError(w, errors.New(custom_errors.ErrInvalidLoginCode), http.StatusUnauthorized)
		return
	}

	// Get application type from context (set by middleware)
	applicationType := getApplicationTypeFromContext(r)
	if applicationType == "" {
		common.RespondError(w, errors.New("application type not found in context"), http.StatusInternalServerError)
		return
	}

	// Validate application access based on user roles
	err = h.validateApplicationAccess(r.Context(), user.ExternalID, applicationType)
	if err != nil {
		log.Println("Application access validation failed: ", err)
		common.RespondError(w, err, http.StatusForbidden)
		return
	}

	err = h.queries.UpdateLoginCode(r.Context(), postgres.UpdateLoginCodeParams{
		LoginCode: "",
		ID:        user.ID,
	})
	if err != nil {
		log.Println("Failed to erase login code: ", err)
		common.RespondError(w, err)
		return
	}

	// Create access token (short-lived)
	accessClaims := map[string]interface{}{
		"sub":  user.ExternalID,
		"exp":  time.Now().Add(15 * time.Minute).Unix(),
		"type": "access",
	}
	_, accessToken, err := jwtauth.New("HS256", []byte(h.env.Core.JWT_SECRET), nil).Encode(accessClaims)
	if err != nil {
		log.Println("Failed to generate access token: ", err)
		common.RespondError(w, err)
		return
	}

	// Create refresh token (long-lived)
	refreshClaims := map[string]interface{}{
		"sub":  user.ExternalID,
		"exp":  time.Now().Add(30 * 24 * time.Hour).Unix(),
		"type": "refresh",
	}
	_, refreshToken, err := jwtauth.New("HS256", []byte(h.env.Core.JWT_SECRET), nil).Encode(refreshClaims)
	if err != nil {
		log.Println("Failed to generate refresh token: ", err)
		common.RespondError(w, err)
		return
	}

	h.handleSessionTrx(r, w, user.ID, accessToken, refreshToken, applicationType)

	response := LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// SendLoginCode godoc
// @Summary Send login code
// @Description Send login code
// @Tags Auth
// @Accept json
// @Produce json
// @Param body body SendLoginCodeRequest true "Send login code payload"
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/auth/send-login-code [post]
func (h *AuthHandler) SendLoginCode(w http.ResponseWriter, r *http.Request) {
	payload := SendLoginCodeRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	user, err := h.queries.GetUserByEmail(r.Context(), payload.Email)
	if err != nil {
		log.Println("Failed to get user by email: ", err)
		common.RespondError(w, err)
		return
	}

	if user.IsDeleted {
		common.RespondError(w, errors.New(custom_errors.ErrUserNotActive), http.StatusBadRequest)
		return
	}

	loginCode := helper.GenerateRandomFiveDigitsCode()
	err = h.queries.UpdateLoginCode(r.Context(), postgres.UpdateLoginCodeParams{
		LoginCode: loginCode,
		ID:        user.ID,
	})
	if err != nil {
		log.Println("Failed to update login code: ", err)
		common.RespondError(w, err)
		return
	}

	go mailer.New(mailer.MailerConfig{
		MailerURL:   h.env.Mailer.URL,
		MailerToken: h.env.Mailer.TOKEN,
		From:        h.env.Mailer.FROM,
		To:          payload.Email,
		Code:        loginCode,
		UserName:    user.Name,
		TemplateKey: h.env.Mailer.SEND_CODE_TEMPLATE_KEY,
	}).Send()

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// Refresh Token godoc
// @Summary Refresh token
// @Description Refresh token
// @Tags Auth
// @Accept json
// @Produce json
// @Param body body RefreshRequest true "Refresh token payload"
// @Success 200 {object} RefreshSuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/auth/refresh [post]
func (h *AuthHandler) Refresh(w http.ResponseWriter, r *http.Request) {
	payload := RefreshRequest{}

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helpers.New().ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	tokenAuth := jwtauth.New("HS256", []byte(h.env.Core.JWT_SECRET), nil)

	// Verify and decode the token
	token, err := jwtauth.VerifyToken(tokenAuth, payload.RefreshToken)
	if err != nil {
		log.Println("Failed to verify token: ", err)
		common.RespondError(w, err, http.StatusUnauthorized)
		return
	}

	// Validate token claims
	claims, err := token.AsMap(r.Context())
	if err != nil {
		log.Println("Failed to get token claims: ", err)
		common.RespondError(w, err, http.StatusUnauthorized)
		return
	}

	// Check token type
	if claims["type"] != "refresh" {
		log.Println("Invalid token type")
		common.RespondError(w, errors.New("invalid token type"), http.StatusUnauthorized)
		return
	}

	// Verify subject exists
	subject, ok := claims["sub"].(string)
	if !ok || subject == "" {
		log.Println("Invalid token subject")
		common.RespondError(w, errors.New("invalid token subject"), http.StatusUnauthorized)
		return
	}

	// Create new access token
	newAccessClaims := map[string]interface{}{
		"sub":  subject,
		"exp":  time.Now().Add(15 * time.Minute).Unix(),
		"type": "access",
	}
	_, newAccessToken, err := tokenAuth.Encode(newAccessClaims)
	if err != nil {
		log.Println("Failed to generate new access token: ", err)
		common.RespondError(w, err)
		return
	}

	user, err := h.queries.GetUserByExternalID(r.Context(), subject)
	if err != nil {
		log.Println("Failed to get user by external id: ", err)
		common.RespondError(w, err)
		return
	}

	// Get application type from context (set by middleware)
	applicationType := getApplicationTypeFromContext(r)
	if applicationType == "" {
		common.RespondError(w, errors.New("application type not found in context"), http.StatusInternalServerError)
		return
	}

	// Validate application access based on user roles
	err = h.validateApplicationAccess(r.Context(), user.ExternalID, applicationType)
	if err != nil {
		log.Println("Application access validation failed: ", err)
		common.RespondError(w, err, http.StatusForbidden)
		return
	}

	h.handleSessionTrx(r, w, user.ID, newAccessToken, payload.RefreshToken, applicationType)

	response := RefreshResponse{
		AccessToken: newAccessToken,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// Logout godoc
// @Summary Logout
// @Description Logout
// @Tags Auth
// @Security Bearer
// @Accept json
// @Produce json
// @Success 200 {object} EmptySuccessResponse "ok"
// @Failure 400 {object} common.ErrorResponse "bad request"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "internal server error"
// @Router /v1/auth/logout [post]
func (h *AuthHandler) Logout(w http.ResponseWriter, r *http.Request) {
	user := r.Context().Value(middlewares.UserContextKey).(middlewares.User)

	err := h.queries.InvalidateSession(r.Context(), user.SessionID)
	if err != nil {
		log.Println("Failed to invalidate session: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

func (h *AuthHandler) handleSessionTrx(r *http.Request, w http.ResponseWriter, userID int32, accessToken, refreshToken, applicationType string) {
	//begin transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	// Check for existing active session for this application type
	session, err := qtx.CheckIfExistsActiveSessionByAppType(r.Context(), postgres.CheckIfExistsActiveSessionByAppTypeParams{
		UserID:          userID,
		ApplicationType: applicationType,
	})
	if err != nil && err.Error() != "no rows in result set" {
		log.Println("Failed to check if session exists: ", err)
		common.RespondError(w, err)
		return
	}

	// If there's an active session for this application type, invalidate it
	if session.ID != 0 {
		err = qtx.InvalidateSession(r.Context(), session.ID)
		if err != nil {
			log.Println("Failed to invalidate session: ", err)
			common.RespondError(w, err)
			return
		}
	}

	// Create new session with application type
	err = qtx.CreateSession(r.Context(), postgres.CreateSessionParams{
		AccessToken:     accessToken,
		RefreshToken:    refreshToken,
		Device:          r.Header.Get("User-Agent"),
		Ip:              r.RemoteAddr,
		UserID:          userID,
		ApplicationType: applicationType,
	})

	if err != nil {
		log.Println("Failed to create session: ", err)
		common.RespondError(w, err)
		return
	}

	err = tx.Commit(r.Context())
	if err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}
}
